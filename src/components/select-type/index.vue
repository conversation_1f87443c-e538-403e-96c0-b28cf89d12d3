<template>
    <view class="flex-space-between yd-select" @click="open">
        <slot name="render">
            <view class="select-placeholder">                 
                <text v-if="selectText" class="select-text">
                    {{ selectText }}
                </text>
                <text v-else>
                    {{ placeholder }}
                </text>
            </view>
            <view class="select-right-icon">
                <uni-icons :type="iconType" size="14" color="#999" />
            </view>
        </slot>
        <!--  -->
        <uni-popup ref="popup" type="bottom" border-radius="10px 10px 0 0" safeArea background-color="#fff" @change="changePopup">
            <view :style="`height:${height}`" class="popup-container">
                <slot name="title">
                    <view class="text-center title">
                        {{ title }}
                    </view>
                </slot>
                <view class="content">
                    <slot>
                        <picker-view :immediate-change="true" @change="changePicker" :value="selectVal" :indicator-style="indicatorStyle" class="content-picker-view">
                            <picker-view-column>
                                <view class="picker-view-item" v-for="(item, index) in data" :key="index">
                                    <text class="picker-view-item__text" :class="{ active: item.value === selectVal[0] }">
                                        {{ item.label || item }}
                                    </text>
                                </view>
                            </picker-view-column>
                        </picker-view>
                    </slot>
                </view>
                <slot name="footer">
                    <view class="flex-space-evenly footer" :style="footerStyle">
                        <button plain class="btn-info btn btn-plain text-center" @click="close">关闭</button>
                        <button class="btn-primary btn text-center" type="primary" @click="submit">确定</button>
                    </view>
                </slot>
            </view>
        </uni-popup>
    </view>
</template>

<script>
import {nextTick} from "vue"
export default {
    props: {
        value: {
            type: Array,
            default: () => []
        },
        data: {
            type: Array,
            default: () => []
        },
        placeholder: {
            type: String,
            default: ""
        },
        title: {
            type: String,
            default: ""
        },
        indicatorStyle: {
            type: String,
            default: `height: 48px;`
        },
        openOther: {
            type: Boolean,
            default: false
        },
        height:{
            type: String,
            default: `auto`
        },
        footerStyle:{
            type: String,
            default: ``
        },
        clickable:{
            type: Boolean,
            default: true
        }
    },
    watch: {
        value: {
            handler(v) {
                this.selectVal = [...v]
                if (v.length) {
                    const value = this.selectVal[0] || 0
                    const idx = this.data.findIndex((item) => item.value == value)
                    this.selectText = this.data[idx]?.label ?? this.data[idx]
                }
                if (v.length === 0) {
                    this.selectVal = [this.data[0]?.value || 0]
                }
            },
            immediate: true
        }
    },
    data() {
        return {
            show: false,
            selectText: "",
            selectVal: []
        }
    },
    computed: {
        iconType() {
            return this.show ? "down" : "right"
        }
    },
    methods: {
        changePicker(e) {
            const val = e.detail.value
            console.log('val----',val)
            this.selectVal = val
        },
        changePopup(e) {
            this.show = e.show
        },
        submit() {
            nextTick(()=>{
                this.$emit("update", this.selectVal)
                const idx = this.selectVal[0] || 0
                this.selectText = this.data[idx]?.label ?? this.data[idx]
                this.close()
            })
        },
        open() {
            if(!this.clickable){
                return 
            }
            if (this.openOther) {
                this.$emit("open")
            } else {
                this.$refs.popup.open("bottom")
            }
        },
        close() {
            this.$refs.popup.close()
        }
    }
}
</script>

<style lang="scss" scoped>
.yd-select {
    position: relative;
    min-height: 100%;
    :deep(.uni-picker-view-indicator){
        border-top: 1px solid #F0F0F0;
        border-bottom: 1px solid #F0F0F0;
    }
    .select-placeholder {
        color: #999;
        font-size: 12px;
        padding-left: 10px;

        .select-text{
            color: #2c2c36;
        }
    }
    .select-right-icon {
        position: absolute;
        right: 0;
        top: 0;
        color: #999;
        font-size: 12px;

        display: flex;
        height: 100%;
        align-items: center;
    }

    .popup-container {
        min-height: 300px;
        position: relative;
        .title {
            font-weight: 600;
            font-size: 14px;
            color: #000000;
            text-align: center;
            padding: 15px 0;
        }
        .btn {
            margin: 0;
            font-size: 14px !important;
            height: 40px;
            line-height: 40px;
            border-radius: 20px;
            min-width: 111px;
        }
        .content {
            .content-picker-view {
                min-width: 750rpx;
                height: 300rpx;
                .picker-view-item {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .picker-view-item__text {
                    text-align: center;
                    color: #666666;
                    font-size: 14px;
                }
                .picker-view-item__text.active {
                    color: $uni-primary;
                }
            }
        }

        .footer {
            padding: 17px 0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }
    }
}
</style>
