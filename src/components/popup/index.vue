
<template>
    <uni-popup class="popup" ref="popup" :background-color="backgroundColor" @change="change" :type="type" @close="close" :borderRadius="borderRadius" :mask-click="maskClick">
		<view class="popup-content">
           <view class="handle-card">
                <view class="handle-card-close" @click="close">
                   <uni-icons type="closeempty" size="20" color="#AAAAAA"></uni-icons>
               </view>
               <view class="handle-card-title">
                    <slot name="title">
                        {{title}}
                    </slot>
               </view>
               <view class="handle-card-btn primary" @click="submit">{{ t('confirm') }}</view>
           </view>
           <slot></slot>
        </view>
	</uni-popup>
</template>

<script setup>
    import { t } from '@/lang';
    import { onMounted,reactive, watch } from 'vue';
    const emit = defineEmits(['update:open','submit'])
    const props = defineProps({
        open: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: 'bottom'
        },
        borderRadius: {
            type: String,
            default: '12px 12px 0px 0px'
        },
        maskClick: {
            type: Boolean,
            default: true
        },
        backgroundColor: {
            type: String,
            default: '#F5F5F7'
        }

    })
    const popup = ref(null)

    const state = reactive({
        
    })

    watch(()=>props.open,(newVal)=>{
        if(newVal){
            popup.value?.open(props.type)
        }else{
            popup.value?.close()
            
        }        
    },{
        immediate:true
    })

    function close(){
        popup.value?.close()
        emit('update:open',false)
    }

    function submit(){
        emit('submit')
    }


    function change(e){
        emit('update:open',e.show)
    }   
</script>


<style lang="scss" scoped>
    .popup{
        position: relative;
        .popup-content{
            min-height: 200px;
        }
        .handle-card{
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 0.5px solid #AAAAAA;
            .handle-card-close{
                padding: 23px 20px 24px 25px;
                color: #AAAAAA;
            }
            .handle-card-title{
                flex: 1;
                text-align: center;
                font-size: 20px;
            }
            .handle-card-btn{
                padding: 23px 26px 24px 25px;
                font-size: 20px;                
            }
        }
    }
</style>