
import { defineStore } from 'pinia';


const useUserStore = defineStore('user', {
    state: () => {
        return {
            user: {               
                userInfo: {},
                sexList: [
                   
                ],
                regionTree: [],
                typeTree:[]
            }
        };
    },
    getters: {
        userInfo(state) {
            return state.user.userInfo
        },
        sexList(state) {
            return [
                
            ]
        },
        hotRegion(state){            
            return  state.user.regionTree.filter(city => city.hotBol).map(i=>{
                const arr =  i.regionName.split("-")
                const regionName = arr[arr.length-1]
                return {
                    ...i,
                    hot_regionName:regionName
                }
            })
        },
        regionTree(state) {
            const letters = Array.from({length: 26}, (_, i) => String.fromCharCode(65 + i));
            const groupedCities = letters.reduce((acc, letter) => {
                const matchedCities = state.user.regionTree.filter(city => 
                    city.regionSort === letter
                );
                if (matchedCities.length > 0) {
                    acc[letter] = matchedCities;
                }
                return acc;
            }, {});
            return groupedCities
        },
        allRegionTree(state){
            return state.user.regionTree
        },
        typeTree(state){
            return state.user.typeTree
        }
    },
    actions: {       
        setUserInfo(userInfo) {
            this.user.userInfo = userInfo
        },
        setRegionTree(regionTree) {
            this.user.regionTree = regionTree
        },
        setTypeTree(tree){
            this.user.typeTree = tree
        }
    },

    persist: {
        key: "user",
        paths: ['user'],
        debug: import.meta.env.VITE_USER_NODE_ENV === "production",
        beforeRestore: (ctx) => {
            console.log(`beforeRestore '${ctx.store.$id}'`)
        },
        afterRestore: (ctx) => {
            console.log(`afterRestore '${ctx.store.$id}'`)
        }
    }
})

export default useUserStore


