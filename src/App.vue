<script>


// import pageAnimation from './components/page-animation'
// #ifdef  H5
import VConsole from "vconsole"
// #endif



['redirectTo', 'navigateTo'].forEach(method => {
    uni.addInterceptor(method, {
        success(args) {
            // console.log('进入页面', args)            
        }
    })
})

export default {
    // mixins: [pageAnimation],
    onLaunch: function () {
        console.log("App ", import.meta.env.MODE)
        // #ifdef  H5
        const isRun = ['development'].includes(process.env.NODE_ENV)
        if(isRun){
        // eslint-disable-next-line no-unused-vars
            const vConsole = new VConsole();
        }
        // #endif      

       
    },
    data() {
        return {
        }
    },
    methods: {
       
    },
    onLoad() {

    },
    mounted() {
        document.addEventListener('touchstart', function (event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        }, { passive: false });

        document.addEventListener('gesturestart', function (event) {
            event.preventDefault();
        });
    },
    onShow: function () {
        console.log('onShow')
    },
    onHide: function () {
        console.log("App Hide")
    }
}
</script>

<style lang="scss">
// @import '@dcloudio/uni-ui/lib/uni-scss/index.scss';

// @import "@/static/iconfont.css";

/* 每个页面公共css */
page {
    font-family: PingFangSC, PingFang SC;
    font-size: 28rpx;
    color: #4D4D4D;
}

:deep(.uni-nav-bar-text) {
    font-size: 16px !important;
    font-weight: 600 !important;
}

</style>
