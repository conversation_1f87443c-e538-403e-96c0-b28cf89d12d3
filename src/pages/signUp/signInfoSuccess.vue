<template>
  <view class="signInfoSuccess">
    <div class="successBox">
      <view>
        <image src="@/static/image/success.png" class="successImg"></image>
      </view>

      <view class="title"> 恭喜你：{{ personalInfo.name }}</view>
      <view class="content"> 你已成功报名xxx职业学校2025批次语文专业</view>
    </div>

    <view class="bottom-fixed">
      <button class="submit-btn" @click="handleSubmit">完成</button>
    </view>
  </view>
</template>

<script setup>
import { useSafeStorage } from "@/hooks/useSafeStorage";

// 获取缓存中选中的专业对象
const personalInfo = useSafeStorage("personalInfo", {});

const handleSubmit = () => {
  uni.navigateTo({
    url: "/pages/home/<USER>",
  });
};
</script>

<style scoped>
.signInfoSuccess {
  background: #f6f6f6;
  height: 100vh;
  position: relative;
}
.successBox {
  display: flex;
  flex-direction: column;
  align-items: center;

  justify-content: center;
}
.successImg {
  width: 80px;
  height: 80px;
}

.title {
  font-weight: 600;
  font-size: 34rpx;
  color: #333333;
}

.content {
  font-weight: 400;
  font-size: 28rpx;
  color: #8c8c8c;
}

.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;

  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #00b781;
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
</style>
