<template>
  <view class="upload-container">
    <view class="lineBox"></view>
    <!-- 主内容区域 -->
    <view class="main-content">
      <!-- 页面标题 -->
      <view class="page-header">
        <text class="page-title">上传附件</text>
      </view>

      <!-- 上传区域循环 -->
      <view
        class="upload-section"
        v-for="(item, index) in uploadItems"
        :key="index"
      >
        <view class="section-title">{{ item.title }}</view>

        <!-- 上传区域 - 未上传状态 -->
        <view
          :class="getUploadAreaClass(item.type)"
          v-if="!item.imageUrl"
          @click="handleUpload(index)"
        >
          <view v-if="['id'].includes(item.type)" class="id_box">
            <uni-icons
              type="camera-filled"
              :size="getIconSize(item.type)"
              color="#cccccc"
            ></uni-icons>
            <text class="upload-text">上传你的身份证{{ item.title }}</text>
          </view>
          <uni-icons v-else type="plusempty" color="#DCDCDC"></uni-icons>
        </view>

        <!-- 图片预览区域 - 已上传状态 -->
        <view :class="getPreviewAreaClass(item.type)" v-else>
          <image
            class="preview-image"
            :src="item.imageUrl"
            :mode="getImageMode(item.type)"
          />
          <view class="remove-btn" @click="removeImage(index)">
            <uni-icons type="clear" size="16" color="#ffffff"></uni-icons>
          </view>
          <!-- 上传状态指示器 -->
          <view class="upload-status" v-if="item.uploading">
            <uni-load-more
              status="loading"
              content-text="上传中"
            ></uni-load-more>
          </view>
        </view>

        <!-- 错误信息显示 -->
        <view class="error-message" v-if="item.error">
          <uni-icons type="info" size="14" color="#ff4d4f"></uni-icons>
          <text class="error-text">{{ item.error }}</text>
        </view>

        <!-- 格式说明 -->
        <view class="format-notice" v-if="!['id'].includes(item.type)">
          仅支持上传jpeg/jpg/png格式文件，单个文件不能超过1M,单次最多可选1张
        </view>
      </view>
    </view>

    <!-- 固定底部按钮区域 -->
    <view class="fixed-bottom-buttons">
      <!-- 按钮容器 -->
      <view class="button-container">
        <!-- 下一步按钮 -->
        <button class="next-btn" @click="handleSubmit">
          <text>下一步</text>
        </button>

        <!-- 跳过按钮 -->
        <view class="skip-btn" @click="handleSkip">
          <text>跳过</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";

import http from "@/utils/request";

// 常量定义
const MAX_FILE_SIZE = 1024 * 1024; // 1MB
const ALLOWED_TYPES = ["image/jpeg", "image/jpg", "image/png"];
const ALLOWED_EXTENSIONS = ["jpeg", "jpg", "png"];

// 上传项目配置 - 包含附件类型分类
const uploadItems = ref([
  {
    title: "人脸照片",
    value: "faceUrl",
    type: "standard", // 标准正方形样式
    imageUrl: "",
    error: "",
    uploading: false,
  },
  {
    title: "人像面：",
    value: "idCardFrontUrl",
    type: "id", // 身份证样式
    imageUrl: "",
    error: "",
    uploading: false,
  },
  {
    title: "国徽面：",
    value: "idCardBackUrl",
    type: "id", // 身份证样式
    imageUrl: "",
    error: "",
    uploading: false,
  },
  {
    title: "成绩单附件",
    value: "transcriptUrl",
    type: "standard", // 标准正方形样式
    imageUrl: "",
    error: "",
    uploading: false,
  },
  {
    title: "毕业照附件",
    value: "diplomaUrl",
    type: "standard", // 标准正方形样式
    imageUrl: "",
    error: "",
    uploading: false,
  },
]);

/**
 * 根据附件类型获取上传区域CSS类名
 * @param {string} type - 附件类型 ('standard' | 'id')
 * @returns {string} CSS类名
 */
const getUploadAreaClass = (type) => {
  return type === "id" ? "upload-area-id" : "upload-area-standard";
};

/**
 * 根据附件类型获取预览区域CSS类名
 * @param {string} type - 附件类型 ('standard' | 'id')
 * @returns {string} CSS类名
 */
const getPreviewAreaClass = (type) => {
  return type === "id" ? "preview-area-id" : "preview-area-standard";
};

/**
 * 根据附件类型获取图标大小
 * @param {string} type - 附件类型
 * @returns {number} 图标大小
 */
const getIconSize = (type) => {
  return type === "id" ? 40 : 48;
};

/**
 * 根据附件类型获取图片显示模式
 * @param {string} type - 附件类型
 * @returns {string} 图片模式
 */
const getImageMode = (type) => {
  return type === "id" ? "aspectFit" : "aspectFill";
};

/**
 * 根据附件类型获取上传提示文本
 * @param {string} type - 附件类型
 * @returns {string} 提示文本
 */
const getUploadHint = (type) => {
  return type === "id" ? "请确保证件信息清晰完整" : "支持拍照或从相册选择";
};

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小字符串
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名
 */
const getFileExtension = (filename) => {
  return filename.split(".").pop().toLowerCase();
};

/**
 * 验证文件类型
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否为允许的类型
 */
const validateFileType = (filePath) => {
  console.log(filePath, "1123123123123");

  const extension = getFileExtension(filePath);
  console.log(extension, "21312312");
  return ALLOWED_EXTENSIONS.includes(extension);
};

/**
 * 处理图片上传逻辑
 * @param {number} index - 上传项目索引
 */
const handleUpload = async (index) => {
  try {
    // 清除之前的错误
    uploadItems.value[index].error = "";

    // 调用 uni-app 选择图片 API
    const result = await new Promise((resolve, reject) => {
      uni.chooseImage({
        count: 1,
        sourceType: ["album", "camera"], // 支持相册和拍照
        sizeType: ["compressed"], // 使用压缩图
        success: resolve,
        fail: reject,
      });
    });

    if (result.tempFiles && result.tempFiles.length > 0) {
      const tempFile = result.tempFiles[0];

      console.log(tempFile, "tempFile");

      // 文件大小验证
      if (tempFile.size > MAX_FILE_SIZE) {
        uploadItems.value[index].error = `文件大小不能超过 ${formatFileSize(
          MAX_FILE_SIZE
        )}`;
        return;
      }

      // 文件类型验证
      if (!validateFileType(tempFile.name)) {
        uploadItems.value[index].error = "仅支持 JPEG/JPG/PNG 格式的图片";
        return;
      }

      // 设置预览图片
      // uploadItems.value[index].imageUrl = tempFile.path;

      // 开始上传到服务器
      await uploadToServer(tempFile, index);
    }
  } catch (error) {
    console.error("选择图片失败:", error);
    uploadItems.value[index].error = "选择图片失败，请重试";

    // 显示错误提示
    uni.showToast({
      title: "选择图片失败",
      icon: "error",
      duration: 2000,
    });
  }
};

/**
 * 上传文件到服务器
 * @param {Object} file - 文件对象
 * @param {number} index - 上传项目索引
 */
const uploadToServer = async (file, index) => {
  console.log(file, "file1111111");
  uploadItems.value[index].uploading = true;
  try {
    const response = await new Promise((resolve, reject) => {
      http
        .uploadFile(file.path, "enrollmentReport")
        .then((res) => {
          console.log(res, "上传项目索引123123123123123213213123");
          resolve(res);
        })
        .catch((err) => {
          reject(err);
        });
    });

    // 处理服务器响应
    if (response.status === 1) {
      uploadItems.value[index].imageUrl = response.url;

      uploadItems.value[index].uploading = false;

      // 显示成功提示
      uni.showToast({
        title: "上传成功",
        icon: "success",
        duration: 1500,
      });
    } else {
      throw new Error(responseData.message || "上传失败");
    }
  } catch (error) {
    console.error("上传失败:", error);
    uploadItems.value[index].error = "上传失败，请重试";
    uploadItems.value[index].uploading = false;

    // 显示错误提示
    uni.showToast({
      title: "上传失败",
      icon: "error",
      duration: 2000,
    });
  }
};

/**
 * 移除已上传的图片
 * @param {number} index - 项目索引
 */
const removeImage = (index) => {
  // 确认删除
  uni.showModal({
    title: "确认删除",
    content: "确定要删除这张图片吗？",
    success: (res) => {
      if (res.confirm) {
        // 重置项目状态
        uploadItems.value[index].imageUrl = "";
        uploadItems.value[index].error = "";
        uploadItems.value[index].uploading = false;

        uni.showToast({
          title: "已删除",
          icon: "success",
          duration: 1000,
        });
      }
    },
  });
};

/**
 * 处理跳过操作
 */
const handleSkip = () => {
  uni.showModal({
    title: "确认跳过",
    content: "确定要跳过此步骤吗？",
    success: (res) => {
      if (res.confirm) {
        // 跳转到下一页
        // 清除缓存
        uni.navigateTo({
          url: "/pages/checkIn/immediatelyCheckIn",
        });
      }
    },
  });
};

/**
 * 处理下一步提交
 */
const handleSubmit = async () => {
  // 把收集到的图片放进缓存中
  uni.setStorageSync("checkIn-imgUrl", JSON.stringify(uploadItems.value));

  // 跳转到下一页
  uni.navigateTo({
    url: "/pages/checkIn/immediatelyCheckIn",
  });
};
</script>

<style scoped>
/* ================================ */
/* 基础容器样式 */
/* ================================ */
.upload-container {
  position: relative;
  min-height: 100vh;
  background: #ffffff;
}

.lineBox {
  height: 20rpx;
  background: #f6f6f6;
}

/* 主内容区域 - 添加底部padding避免被固定按钮遮挡 */
.main-content {
  padding: 24rpx 50rpx 220rpx 50rpx;
  /* padding-bottom: 120px;  */
}

/* 页面标题 */
.page-header {
  margin-bottom: 24rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}

/* 上传区域样式 */
.upload-section {
  margin-bottom: 32rpx;
  background: #ffffff;
}

.section-title {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;

  margin-bottom: 24rpx;
}

/* ================================ */
/* 标准上传区域样式 - 人脸照片、成绩单、毕业照 */
/* ================================ */
.upload-area-standard {
  width: 192rpx;
  height: 192rpx;
  background: #f7f7f7;
  border-radius: 16rpx;
  /* border: 1px dashed #d9d9d9; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 标准预览区域样式 */
.preview-area-standard {
  position: relative;
  width: 192rpx;
  height: 192rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f7f7f7;
}

/* ================================ */
/* 身份证上传区域样式 - 身份证正面、反面 */
/* ================================ */
.upload-area-id {
  height: 380rpx;
  background: #f7f7f7;
  border-radius: 12rpx;
  border: 1px dashed #d9d9d9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 0 auto;
}

/* 身份证预览区域样式 */
.preview-area-id {
  position: relative;
  height: 380rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background: #f7f7f7;
}

/* ================================ */
/* 通用样式 */
/* ================================ */
.upload-text {
  margin-top: 12rpx;
  font-weight: 500;
  font-size: 28rpx;
  color: #8c8c8c;
}

.upload-hint {
  font-size: 11px;
  color: #999999;
  line-height: 1.4;
  margin-top: 4px;
  text-align: center;
  max-width: 120px;
}

/* 预览图片样式 */
.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 删除按钮样式 */
.remove-btn {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 24px;
  height: 24px;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

/* 上传状态指示器 */
.upload-status {
  position: absolute;
  bottom: 8px;
  left: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 3px 6px;
}

.success-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 错误信息样式 */
.error-message {
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-text {
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.4;
  margin-left: 4px;
}

/* 文件信息样式 */
.file-info {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.file-size,
.file-format {
  font-size: 11px;
  color: #666666;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

/* ================================ */
/* 固定底部按钮区域样式 */
/* ================================ */
.fixed-bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  /* border-top: 1px solid #f0f0f0; */
  z-index: 1000;
  /* box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); */
  /* 兼容安全区域 */
  padding-bottom: env(safe-area-inset-bottom);
}

/* 按钮容器 */
.button-container {
  display: flex;
  flex-direction: column;
  padding: 16px 20px;
  align-items: center;
}

/* 下一步按钮样式 */
.next-btn {
  width: 100%;
  height: 80rpx;
  background-color: #00b781 !important;

  font-weight: 400;
  font-size: 28rpx;
  color: #ffffff !important;

  border-radius: 40rpx;

  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.skip-btn {
  padding-top: 28rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #8c8c8c;
}
.format-notice {
  padding-top: 16rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #8f8f8f;
}

.id_box {
  display: flex;
  align-items: center;
  flex-direction: column;
}
</style>
