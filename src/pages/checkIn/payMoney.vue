<template>
  <view class="payment-container">
    <!-- 支付卡片 -->
    <view class="payment-card">
      <!-- 倒计时区域 -->
      <view class="countdown-section">
        <text class="countdown-text"
          >支付剩余时间 {{ formatTime(countdown) }}</text
        >
      </view>

      <!-- 支付金额区域 -->
      <view class="amount-section">
        <text class="amount-symbol">¥</text>
        <text class="amount-value">{{ paymentAmount }}</text>
      </view>

      <!-- 订单信息区域 -->
      <view class="order-section">
        <view class="order-row">
          <text class="order-label">订单信息：</text>
          <text class="order-value">{{ orderInfo }}</text>
        </view>
      </view>

      <!-- 支付方式区域 -->
      <view class="payment-method-section">
        <view class="payment-method" @click="selectPaymentMethod">
          <view class="method-left">
            <image
              class="wechat-icon"
              src="@/static/image/weixin.png"
              mode="aspectFit"
            ></image>
            <text class="method-text">微信支付</text>
          </view>
          <view class="method-right">
            <view class="check-icon" :class="{ checked: isWechatSelected }">
              <text class="check-text" v-if="isWechatSelected">✓</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 确认支付按钮 -->
    <view class="payment-button-section">
      <button
        class="confirm-payment-btn"
        :class="{ disabled: !canPay }"
        @click="handlePayment"
      >
        确认支付
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";

// 支付相关数据
const paymentAmount = ref("1200.00");
const orderInfo = ref("2024下学年学费：2024年期初订单");
const isWechatSelected = ref(true);
const countdown = ref(15 * 60); // 15分钟倒计时（秒）
const timer = ref(null);

// 支付状态
const paymentStatus = ref("pending"); // pending, processing, success, failed, timeout

// 计算属性
const canPay = computed(() => {
  return (
    isWechatSelected.value &&
    countdown.value > 0 &&
    paymentStatus.value !== "processing"
  );
});

// 格式化倒计时显示
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
};

// 开始倒计时
const startCountdown = () => {
  timer.value = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      // 倒计时结束
      stopCountdown();
      handlePaymentTimeout();
    }
  }, 1000);
};

// 停止倒计时
const stopCountdown = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

// 支付超时处理
const handlePaymentTimeout = () => {
  paymentStatus.value = "timeout";

  uni.showModal({
    title: "支付超时",
    content: "支付时间已超时，请重新发起支付",
    showCancel: false,
    confirmText: "确定",
    success: () => {
      // 返回上一页或跳转到订单页面
      uni.navigateBack();
    },
  });
};

// 选择支付方式
const selectPaymentMethod = () => {
  isWechatSelected.value = !isWechatSelected.value;

  // 这里可以扩展其他支付方式
  if (isWechatSelected.value) {
    uni.showToast({
      title: "已选择微信支付",
      icon: "none",
      duration: 1000,
    });
  }
};

// 处理支付
const handlePayment = async () => {
  if (!canPay.value) {
    if (countdown.value <= 0) {
      uni.showToast({
        title: "支付已超时",
        icon: "none",
      });
    } else if (!isWechatSelected.value) {
      uni.showToast({
        title: "请选择支付方式",
        icon: "none",
      });
    }
    return;
  }

  try {
    paymentStatus.value = "processing";

    // 显示支付加载状态
    uni.showLoading({
      title: "正在调起支付...",
    });

    // 调用微信支付
    await initiateWechatPayment();
  } catch (error) {
    paymentStatus.value = "failed";
    uni.hideLoading();

    console.error("支付失败:", error);

    uni.showModal({
      title: "支付失败",
      content: error.message || "支付过程中出现错误，请重试",
      showCancel: true,
      cancelText: "取消",
      confirmText: "重试",
      success: (res) => {
        if (res.confirm) {
          paymentStatus.value = "pending";
        }
      },
    });
  }
};

// 发起微信支付
const initiateWechatPayment = async () => {
  try {
    // 第一步：创建支付订单
    const orderData = await createPaymentOrder();

    // 第二步：调用微信支付
    const paymentResult = await requestWechatPayment(orderData);

    // 第三步：处理支付结果
    await handlePaymentResult(paymentResult);
  } catch (error) {
    throw error;
  }
};

// 创建支付订单
const createPaymentOrder = () => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: "https://api/payment/create-order",
      method: "POST",
      data: {
        amount: parseFloat(paymentAmount.value) * 100, // 转为分
        orderInfo: orderInfo.value,
        paymentMethod: "wechat",
        timestamp: Date.now(),
      },
      header: {
        "Content-Type": "application/json",
        // 添加授权token等
        Authorization: "Bearer " + uni.getStorageSync("token"),
      },
      success: (res) => {
        if (res.data.code === 200) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data.message || "创建订单失败"));
        }
      },
      fail: (err) => {
        reject(new Error("网络错误，请检查网络连接"));
      },
    });
  });
};

// 调用微信支付
const requestWechatPayment = (orderData) => {
  return new Promise((resolve, reject) => {
    // #ifdef MP-WEIXIN
    // 微信小程序支付
    uni.requestPayment({
      provider: "wxpay",
      timeStamp: orderData.timeStamp,
      nonceStr: orderData.nonceStr,
      package: orderData.package,
      signType: orderData.signType,
      paySign: orderData.paySign,
      success: (res) => {
        console.log("微信支付成功:", res);
        resolve({
          status: "success",
          transactionId: res.transactionId || orderData.orderId,
          orderData: orderData,
        });
      },
      fail: (err) => {
        console.error("微信支付失败:", err);

        // 处理不同的错误情况
        if (err.errMsg === "requestPayment:fail cancel") {
          reject(new Error("用户取消支付"));
        } else if (err.errMsg.includes("timeout")) {
          reject(new Error("支付超时，请重试"));
        } else {
          reject(new Error(err.errMsg || "支付失败"));
        }
      },
    });
    // #endif

    // #ifdef APP-PLUS
    // App端微信支付
    plus.payment.request(
      "wxpay",
      {
        appid: orderData.appid,
        partnerid: orderData.partnerid,
        prepayid: orderData.prepayid,
        package: orderData.package,
        noncestr: orderData.noncestr,
        timestamp: orderData.timestamp,
        sign: orderData.sign,
      },
      (result) => {
        console.log("App微信支付成功:", result);
        resolve({
          status: "success",
          transactionId: result.rawdata || orderData.orderId,
          orderData: orderData,
        });
      },
      (error) => {
        console.error("App微信支付失败:", error);
        reject(new Error(error.description || "支付失败"));
      }
    );
    // #endif

    // #ifdef H5
    // H5端处理（通常跳转到微信支付页面）
    if (orderData.mwebUrl) {
      // 跳转到微信H5支付页面
      window.location.href = orderData.mwebUrl;
    } else {
      reject(new Error("H5支付暂不支持"));
    }
    // #endif
  });
};

// 处理支付结果
const handlePaymentResult = async (paymentResult) => {
  try {
    uni.hideLoading();

    if (paymentResult.status === "success") {
      // 验证支付结果
      const verifyResult = await verifyPaymentResult(paymentResult);

      if (verifyResult.success) {
        paymentStatus.value = "success";
        stopCountdown();

        // 支付成功
        uni.showToast({
          title: "支付成功",
          icon: "success",
          duration: 2000,
        });

        // 延迟跳转到成功页面
        setTimeout(() => {
          uni.redirectTo({
            url: `/pages/payment-success/`,
          });
        }, 2000);
      } else {
        throw new Error("支付验证失败");
      }
    } else {
      throw new Error("支付未完成");
    }
  } catch (error) {
    paymentStatus.value = "failed";
    throw error;
  }
};

// 验证支付结果
const verifyPaymentResult = (paymentResult) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: "https://api/payment/verify",
      method: "POST",
      data: {
        transactionId: paymentResult.transactionId,
        orderId: paymentResult.orderData.orderId,
      },
      header: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + uni.getStorageSync("token"),
      },
      success: (res) => {
        if (res.data.code === 200) {
          resolve({
            success: true,
            data: res.data.data,
          });
        } else {
          reject(new Error(res.data.message || "验证支付结果失败"));
        }
      },
      fail: (err) => {
        reject(new Error("网络错误"));
      },
    });
  });
};

// 生命周期
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.options;

  if (options.amount) {
    paymentAmount.value = options.amount;
  }
  if (options.orderInfo) {
    orderInfo.value = decodeURIComponent(options.orderInfo);
  }

  // 开始倒计时
  startCountdown();
});

onUnmounted(() => {
  // 清理定时器
  stopCountdown();
});

// 监听页面隐藏（用户切换应用）
// #ifdef MP-WEIXIN
import { onHide, onShow } from "@dcloudio/uni-app";

onHide(() => {
  console.log("页面隐藏，暂停倒计时");
  stopCountdown();
});

onShow(() => {
  console.log("页面显示，继续倒计时");
  if (countdown.value > 0 && paymentStatus.value === "pending") {
    startCountdown();
  }
});
// #endif
</script>

<style scoped>
/* 页面容器 */
.payment-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 80rpx 30rpx;
  display: flex;
  flex-direction: column;
}

/* 支付卡片 */
.payment-card {
  /* background-color: #ffffff;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08); */
  flex: 1;
}

/* 倒计时区域 */
.countdown-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.countdown-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}

/* 支付金额区域 */
.amount-section {
  text-align: center;
  margin-bottom: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-symbol {
  font-size: 48rpx;
  color: #333333;
  font-weight: 400;
  margin-right: 8rpx;
  margin-top: -10rpx;
}

.amount-value {
  font-size: 96rpx;
  color: #333333;

  line-height: 1;

  font-weight: 600;

  color: #333333;
}

/* 订单信息区域 */
.order-section {
  margin-bottom: 24rpx;
  padding: 38rpx 24rpx;

  background: #ffffff;
  border-radius: 12rpx;
}

.order-row {
  display: flex;
  align-items: flex-start;
}

.order-label {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.order-value {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  flex: 1;
}

/* 支付方式区域 */
.payment-method-section {
  background: #ffffff;
  border-radius: 12rpx;
  padding: 38rpx 24rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  justify-content: space-between;

  transition: background-color 0.3s ease;
}

.payment-method:active {
  background-color: #f8f9fa;
}

.method-left {
  display: flex;
  align-items: center;
}

.wechat-icon {
  width: 80rpx;
  height: 64rpx;
  margin-right: 24rpx;
}

.method-text {
  font-size: 32rpx;
  color: #333333;
  line-height: 44rpx;
}

.method-right {
  display: flex;
  align-items: center;
}

.check-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.check-icon.checked {
  background-color: #00c853;
  border-color: #00c853;
}

.check-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: bold;
}

/* 确认支付按钮区域 */
.payment-button-section {
  margin-top: 40rpx;
}

.confirm-payment-btn {
  width: 100%;
  height: 88rpx;
  background-color: #00c853;
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  transition: all 0.3s ease;
}

.confirm-payment-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}

.confirm-payment-btn:active:not(.disabled) {
  background-color: #00b248;
  transform: scale(0.98);
}

/* 动画效果 */
.payment-card {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 支付处理中的加载状态 */
.confirm-payment-btn.processing {
  background-color: #999999;
  pointer-events: none;
}
</style>
