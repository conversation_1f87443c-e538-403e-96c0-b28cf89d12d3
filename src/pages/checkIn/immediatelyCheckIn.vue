<template>
  <view class="page-container">
    <view class="segmentation"> </view>
    <view class="form-container">
      <!-- 表单内容 -->
      <view class="form-content">
        <view class="form-item">
          <view class="label">
            <text class="label-text">姓名：</text>
          </view>
          <input
            class="form-input"
            v-model="personalInfo.name"
            placeholder="请输入姓名"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">性别：</text>
          </view>
          <input
            class="form-input"
            v-model="personalInfo.gender"
            placeholder="请输入性别"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">手机号码</text>
          </view>
          <input
            class="form-input"
            v-model="personalInfo.phone"
            placeholder="请输入手机号码"
            type="number"
          />
        </view>

        <view class="form-item">
          <view class="label">
            <text class="label-text">专业：</text>
          </view>
          <input
            class="form-input"
            v-model="selectSpecialty.majorName"
            placeholder="请输入专业："
          />
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button class="submit-btn" @click="handleSubmit">立即报到</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";

const personalInfo = useSafeStorage("checkIn-personalInfo", {});

const selectSpecialty = useSafeStorage("checkIn-majorIntroduce", {});

const allImgUrl = useSafeStorage("checkIn-imgUrl", []);
console.log(allImgUrl,'allImgUrl');


const familyList = useSafeStorage("checkIn-familyInfo", []);

const handleSubmit = async () => {
  try {
    uni.showLoading({
      title: "提交中...",
    });

    // 用 reduce 将 value 和 imageUrl 转换为对象
    const imgResult = allImgUrl.value.reduce((acc, curr) => {
      acc[curr.value] = curr.imageUrl;
      return acc;
    }, {});

    // API请求
    await http.post("/app/enrollment/report/createReport", {
      schoolId: selectSpecialty.value.schoolId,
      planId: selectSpecialty.value.planId,
      enrollmentMajorId: selectSpecialty.value.id,
      ...personalInfo.value,
      ...imgResult,
      familyList: familyList.value,
    });

    uni.hideLoading();

    // 提交成功
    uni.showToast({
      title: "提交成功",
      icon: "success",
      duration: 2000,
    });

    // 成功
    uni.navigateTo({
      url: "/pages/checkIn/checkInSuccess",
    });
  } catch (error) {
    uni.hideLoading();
    console.error("提交失败:", error);
  }
};
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.segmentation {
  height: 20rpx;
  background-color: #f6f6f6;
}

.form-container {
  /* margin: 30rpx; */
  background-color: #ffffff;
  /* border-radius: 20rpx; */
  overflow: hidden;
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); */
}

.form-title {
  background-color: #f6f6f6;

  padding: 40rpx 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  padding: 0 40rpx 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label.required {
  position: relative;
}

.required-star {
  color: #ff4757;
  font-size: 28rpx;
  margin-right: 4rpx;
}

.label-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}

.form-input::placeholder {
  color: #c0c0c0;
}

/* 固定底部按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #00c853 0%, #4caf50 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 响应式适配 */
/* @media (max-width: 480px) {
  .form-container {
    margin: 20rpx;
    border-radius: 16rpx;
  }

  .form-content {
    padding: 0 30rpx 30rpx;
  }

  .label {
    width: 140rpx;
  }

  .form-title,
  .label-text,
  .form-input {
    font-size: 26rpx;
  }
} */

.noBorder {
  :deep(.uni-select) {
    border: none !important;
  }
}
</style>
