<template>
  <view class="page-container">
    <view class="form-container">
      <!-- 表单标题 -->
      <view class="form-title">个人基本信息</view>
      <!-- 表单内容 -->
      <view class="form-content">
        <!-- 姓名 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">姓名</text>
          </view>
          <input
            class="form-input"
            v-model="formData.name"
            placeholder="请输入姓名"
            maxlength="20"
            @blur="validateField('name')"
          />
        </view>

        <!-- 性别 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">性别</text>
          </view>
          <uni-data-select
            class="noBorder"
            :clear="false"
            v-model="formData.gender"
            :localdata="genderOptions"
            placeholder="请选择"
                   @change="onGenderChange"
          ></uni-data-select>
        </view>

        <!-- 手机号 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">手机号</text>
          </view>
          <input
            class="form-input"
            v-model="formData.phone"
            placeholder="请输入手机号"
            type="number"
            maxlength="11"
            @blur="validateField('phone')"
          />
        </view>

        <!-- 专业 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">专业</text>
          </view>
          <input
            class="form-input"
            v-model="selectSpecialty.majorName"
            disabled
          />
          <!-- <uni-data-select
            v-model="formData.major"
            :localdata="majorOptions"
            placeholder="请选择一门课程的问号"
            @change="onMajorChange"
          ></uni-data-select> -->
        </view>

        <!-- 邮箱 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">邮箱</text>
          </view>
          <input
            class="form-input"
            v-model="formData.email"
            placeholder="请输入邮箱"
            type="email"
          />
        </view>

        <!-- 身份证号 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">身份证号</text>
          </view>
          <input
            class="form-input"
            v-model="formData.idCard"
            placeholder="请输入身份证号"
            maxlength="18"
            @blur="validateField('idCard')"
          />
        </view>

        <!-- 出生日期 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">出生日期</text>
          </view>
          <uni-datetime-picker
            v-model="formData.birthday"
            type="date"
            placeholder="请选择出生日期"
            :clear-icon="false"
          ></uni-datetime-picker>
        </view>

        <!-- 政治面貌 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">政治面貌</text>
          </view>
          <uni-data-select
            :clear="false"
            v-model="formData.politicalStatus"
            :localdata="politicalOptions"
            placeholder="请选择"
          ></uni-data-select>
        </view>

        <!-- 民族 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">民族</text>
          </view>
          <uni-data-select
            :clear="false"
            v-model="formData.nation"
            :localdata="ethnicityOptions"
            placeholder="请选择"
          ></uni-data-select>
        </view>

        <!-- 籍贯 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">籍贯</text>
          </view>
          <uni-data-picker
            :clear-icon="false"
            :map="{ text: 'name', value: 'id', children: 'area' }"
            v-model="formData.nativePlace"
            :localdata="provinceOptions"
            popup-title="请选择籍贯"
            @change="onNativePlacechange"
          ></uni-data-picker>
        </view>

        <!-- 户籍所在地 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">户籍所在地</text>
          </view>
          <input
            class="form-input"
            v-model="formData.householdAddress"
            placeholder="请输入户籍所在地"
          />
        </view>

        <!-- 家庭现住址 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">家庭现住址</text>
          </view>
          <input
            class="form-input"
            v-model="formData.address"
            placeholder="请输入家庭现住址"
          />
        </view>

        <!-- 考试总分 -->
        <view class="form-item">
          <view class="label">
            <text class="required-star">*</text>
            <text class="label-text">考试总分</text>
          </view>
          <input
            class="form-input"
            v-model="formData.totalScore"
            placeholder="请输入考试总分"
            type="number"
          />
        </view>

        <!-- 是否住校 -->
        <view class="form-item">
          <view class="label required">
            <text class="required-star">*</text>
            <text class="label-text">是否住校</text>
          </view>
          <uni-data-select
            :clear="false"
            placement="top"
            class="noBorder"
            v-model="formData.isDormitory"
            :localdata="isDormitoryOpt"
            placeholder="请选择"
          ></uni-data-select>
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <view class="bottom-fixed">
      <button
        class="submit-btn"
        :class="{ disabled: !canSubmit }"
        @click="handleSubmit"
      >
        下一步
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import http from "@/utils/request";
import { useSafeStorage } from "@/hooks/useSafeStorage";

import {
  provinceOptions,
  ethnicityOptions,
  politicalOptions,
  genderOptions,
  isDormitoryOpt,
} from "@/utils/china-area-data";

// 获取缓存中选中的专业对象
const selectSpecialty = useSafeStorage("selectSpecialty", {});

// 表单数据
const formData = reactive({
  name: "", // 姓名 *
  gender: "", // 性别 *
  phone: "", // 手机号 *
  email: "", // 邮箱
  idCard: "", // 身份证号
  birthday: "", // 出生日期
  politicalStatus: "", // 政治面貌
  nation: "", // 民族
  nativePlaces: "", // 籍贯
  nativePlace: "", // 籍贯
  householdAddress: "", // 户籍所在地
  address: "", // 家庭现住址
  totalScore: "", // 考试总分
});

// 表单验证错误
const formErrors = reactive({
  name: "",
  gender: "",
  phone: "",
  email: "",
  idCard: "",
});

// 计算属性：是否可以提交
const canSubmit = computed(() => {
  return (
    formData.name.trim() !== "" &&
    formData.gender !== "" &&
    formData.phone.trim() !== "" &&
    Object.values(formErrors).every((error) => error === "")
  );
});

// 验证方法
const validateField = (field) => {
  switch (field) {
    case "name":
      if (!formData.name.trim()) {
        formErrors.name = "请输入姓名";
      } else if (formData.name.length < 2) {
        formErrors.name = "姓名至少2个字符";
      } else {
        formErrors.name = "";
      }
      break;

    case "phone":
      const phoneReg = /^1[3-9]\d{9}$/;
      if (!formData.phone.trim()) {
        formErrors.phone = "请输入手机号";
      } else if (!phoneReg.test(formData.phone)) {
        formErrors.phone = "请输入正确的手机号";
      } else {
        formErrors.phone = "";
      }
      break;

    // case "email":
    //   if (formData.email.trim()) {
    //     const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    //     if (!emailReg.test(formData.email)) {
    //       formErrors.email = "请输入正确的邮箱格式";
    //     } else {
    //       formErrors.email = "";
    //     }
    //   } else {
    //     formErrors.email = "";
    //   }
    //   break;

    case "idCard":
      if (formData.idCard.trim()) {
        const idCardReg =
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!idCardReg.test(formData.idCard)) {
          formErrors.idCard = "请输入正确的身份证号";
        } else {
          formErrors.idCard = "";
        }
      } else {
        formErrors.idCard = "";
      }
      break;
  }

  // 显示错误提示
  if (formErrors[field]) {
    uni.showToast({
      title: formErrors[field],
      icon: "none",
      duration: 2000,
    });
  }
};



// 选择器变化事件
const onGenderChange = (e) => {
  console.log(e,'性别');
  
  formData.gender = e;
  formErrors.gender = "";
};


const onNativePlacechange = (e) => {
  formData.nativePlaces = e.detail.value.map((item) => item.value);
  console.log(formErrors.nativePlaces, "21312312");
};

const submitForm = async () => {
  try {
    // uni.showLoading({
    //   title: "提交中...",
    // });

    // API请求
    // await http.post("/app/enrollment/intention/createSignUp", {
    //   schoolId: selectSpecialty.value.schoolId,
    //   planId: selectSpecialty.value.planId,
    //   enrollmentMajorId: selectSpecialty.value.majorId,
    //   ...formData,
    // });

    // uni.hideLoading();

    // // 提交成功
    // uni.showToast({
    //   title: "提交成功",
    //   icon: "success",
    //   duration: 2000,
    // });

    const formDataObj = {
      ...formData,
    };

    console.log(formDataObj, "11111111");

    uni.setStorageSync("personalInfo", JSON.stringify(formDataObj));

    // 到下一步去确认
    uni.navigateTo({
      url: "/pages/signUp/signInfo",
    });
  } catch (error) {
    console.error("提交失败:", error);
  }
};

// 提交表单
const handleSubmit = () => {
  // 验证必填项
  validateField("name");
  validateField("phone");

  if (!formData.gender) {
    formErrors.gender = "请选择性别";
    uni.showToast({
      title: "请选择性别",
      icon: "none",
    });
    return;
  }

  // if (!formData.major) {
  //   formErrors.major = "请选择专业";
  //   uni.showToast({
  //     title: "请选择专业",
  //     icon: "none",
  //   });
  //   return;
  // }

  if (!canSubmit.value) {
    uni.showToast({
      title: "请完善必填信息",
      icon: "none",
    });
    return;
  }

  const formDataObj = {
    ...formData,
  };

  uni.setStorageSync("checkIn-personalInfo", JSON.stringify(formDataObj));

  // 到下一步去填写家庭成员
  uni.navigateTo({
    url: "/pages/checkIn/familyInfo",
  });
};
</script>

<style scoped>
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.form-container {
  /* margin: 30rpx; */
  background-color: #ffffff;
  /* border-radius: 20rpx; */
  overflow: hidden;
  /* box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06); */
}

.form-title {
  background-color: #f6f6f6;
  padding: 24rpx 46rpx;
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  padding: 0 40rpx 40rpx;
}

.form-item {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 160rpx;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.label.required {
  position: relative;
}

.required-star {
  color: #fd4f45;
  font-size: 24rpx;
  margin-right: 4rpx;
}

.label-text {
  line-height: 34rpx;

  font-weight: 600;
  font-size: 24rpx;
  color: #333333;
}

.form-input {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
  color: #333333;
  padding: 0 20rpx;
  background-color: transparent;
}

.form-input .uni-input-placeholder {
  font-weight: 400;
  font-size: 24rpx;
  color: #bfbfbf;
}

.uni-select__input-placeholder {
  font-weight: 400 !important;
  font-size: 24rpx !important;
  color: #bfbfbf !important;
}

/* uni-ui 组件样式调整 */
:deep(.uni-select) {
  flex: 1;
  border: none !important;
}

:deep(.uni-select .uni-select__input-box) {
  height: 80rpx !important;
  border: none !important;
  background-color: transparent !important;
}

:deep(.uni-select .uni-select__input-text) {
  font-size: 28rpx !important;
  color: #333333 !important;
}

:deep(.uni-select .uni-select__input-placeholder) {
  color: #c0c0c0 !important;
  font-size: 28rpx !important;
}

:deep(.uni-datetime-picker) {
  flex: 1;
}

:deep(.uni-datetime-picker .uni-datetime-picker-view) {
  height: 80rpx !important;
  border: none !important;
  background-color: transparent !important;
}

:deep(.uni-datetime-picker .uni-datetime-picker-text) {
  font-size: 28rpx !important;
  color: #333333 !important;
}

:deep(.uni-datetime-picker .uni-datetime-picker-placeholder) {
  color: #c0c0c0 !important;
  font-size: 28rpx !important;
}

/* 固定底部按钮 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #00c853 0%, #4caf50 100%);
  color: #ffffff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.submit-btn.disabled {
  background: #cccccc;
  color: #999999;
}

.submit-btn:active:not(.disabled) {
  transform: scale(0.98);
  background: linear-gradient(135deg, #00b248 0%, #43a047 100%);
}

/* 响应式适配 */
/* @media (max-width: 480px) {
  .form-container {
    margin: 20rpx;
    border-radius: 16rpx;
  }

  .form-content {
    padding: 0 30rpx 30rpx;
  }

  .label {
    width: 140rpx;
  }

  .form-title,
  .label-text,
  .form-input {
    font-size: 26rpx;
  }
} */

.noBorder {
  :deep(.uni-select) {
    border: none !important;
  }
}
</style>
