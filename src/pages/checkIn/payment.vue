<template>
  <view class="page-container">
    <!-- 缴费列表 -->
    <scroll-view
      scroll-y="true"
      class="scroll-container"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="payment-list">
        <view
          class="payment-item"
          v-for="(item, index) in paymentList"
          :key="index"
        >
          <!-- 缴费标题 -->
          <view class="payment-title">{{ item.title }}</view>

          <!-- 缴费详情 -->
          <view class="payment-details">
            <!-- 缴费时间 -->
            <view class="detail-row">
              <text class="detail-label">缴费时间</text>
              <text class="detail-value">{{ item.paymentPeriod }}</text>
            </view>

            <!-- 缴费账期 -->
            <view class="detail-row">
              <text class="detail-label">缴费账期</text>
              <text class="detail-value">{{ item.paymentTerm }}</text>
            </view>

            <!-- 备注信息 -->
            <view class="detail-row">
              <text class="detail-label">备注信息</text>
              <text class="detail-value">{{ item.remark }}</text>
            </view>

            <!-- 实缴金额 -->
            <view class="detail-row">
              <text class="detail-label">实缴金额</text>
              <text class="detail-value amount">{{ item.amount }}</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 提示信息 -->
    <view class="notice-bar">
      <text class="notice-text"
        >存在不同收款方，请按单次进行缴费，谢谢！</text
      >
    </view>

    <!-- 底部固定区域 -->
    <view class="bottom-fixed">
      <!-- 合计金额 -->
      <view class="total-section">
        <text class="total-label">合计：</text>
        <text class="total-amount">¥{{ totalAmount }}</text>
      </view>

      <!-- 立即缴费按钮 -->
      <button
        class="payment-btn"
        :class="{ disabled: !canPay }"
        @click="handlePayment"
      >
        立即缴费
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from "vue";

const refreshing = ref(false);
const onRefresh = async () => {
  refreshing.value = true;
  try {
    // await loadPaymentList();
    uni.showToast({
      title: "刷新成功",
      icon: "success",
    });
  } catch (error) {
    uni.showToast({
      title: "刷新失败",
      icon: "none",
    });
  } finally {
    refreshing.value = false;
  }
};

// 缴费列表数据
const paymentList = ref([
  {
    title: "2024下学年学费",
    paymentPeriod: "2024.05.15-2025.06.12",
    paymentTerm: "暂时1次缴费",
    remark: "—",
    amount: "¥200.00",
    numericAmount: 200.0,
  },
  {
    title: "2024下学年学费",
    paymentPeriod: "2024.05.15-2025.06.12",
    paymentTerm: "暂时1次缴费",
    remark: "—",
    amount: "¥200.00",
    numericAmount: 200.0,
  },
]);

// 计算总金额
const totalAmount = computed(() => {
  const total = paymentList.value.reduce((sum, item) => {
    return sum + item.numericAmount;
  }, 0);
  return total.toFixed(2);
});

// 是否可以缴费
const canPay = computed(() => {
  return paymentList.value.length > 0 && totalAmount.value > 0;
});

// 处理缴费
const handlePayment = () => {
  if (!canPay.value) {
    uni.showToast({
      title: "暂无可缴费项目",
      icon: "none",
    });
    return;
  }

  // 确认缴费
  uni.showModal({
    title: "确认缴费",
    content: `确认缴费总金额 ¥${totalAmount.value} 元吗？`,
    success: (res) => {
      if (res.confirm) {
        processPayment();
      }
    },
  });
};

// 处理缴费流程
const processPayment = async () => {
  try {
    uni.showLoading({
      title: "处理中...",
    });

    // 模拟支付API调用
    await simulatePayment();

    uni.hideLoading();

    // 支付成功
    uni.showModal({
      title: "缴费成功",
      content: `已成功缴费 ¥${totalAmount.value} 元`,
      showCancel: false,
      confirmText: "确定",
      success: () => {
        // 可以跳转到缴费成功页面或刷新数据
        refreshPaymentList();
      },
    });
  } catch (error) {
    uni.hideLoading();

    uni.showModal({
      title: "缴费失败",
      content: error.message || "缴费失败，请重试",
      showCancel: false,
      confirmText: "确定",
    });
  }
};

// 模拟支付API
const simulatePayment = () => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 模拟90%成功率
      if (Math.random() > 0.1) {
        resolve({
          code: 200,
          message: "支付成功",
          transactionId: "TXN" + Date.now(),
        });
      } else {
        reject(new Error("网络异常，请重试"));
      }
    }, 2000);
  });
};

// 刷新缴费列表
const refreshPaymentList = async () => {
  try {
    // 模拟从API重新获取数据
    // const response = await getPaymentList()
    // paymentList.value = response.data

    // 这里暂时清空列表模拟已缴费
    paymentList.value = [];

    uni.showToast({
      title: "数据已更新",
      icon: "success",
    });
  } catch (error) {
    console.error("刷新数据失败:", error);
  }
};

// 获取缴费列表API（示例）
const getPaymentList = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        data: [
          {
            title: "2024下学年学费",
            paymentPeriod: "2024.05.15-2025.06.12",
            paymentTerm: "暂时1次缴费",
            remark: "—",
            amount: "¥200.00",
            numericAmount: 200.0,
          },
        ],
      });
    }, 1000);
  });
};
</script>

<style scoped>
/* 页面容器 */
.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 200rpx;
}

/* 缴费列表 */
.payment-list {
  padding: 30rpx 30rpx 0;
}

/* 缴费项目 */
.payment-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 缴费标题 */
.payment-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 30rpx;
  line-height: 44rpx;
}

/* 缴费详情 */
.payment-details {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 详情行 */
.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 40rpx;
}

/* 详情标签 */
.detail-label {
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}

/* 详情值 */
.detail-value {
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  text-align: right;
}

/* 金额样式 */
.detail-value.amount {
  color: #ff3b30;
  font-weight: 500;
  font-size: 30rpx;
}

/* 提示信息条 */
.notice-bar {
  margin: 0 30rpx 30rpx;
  background-color: #333333;
  border-radius: 8rpx;
  padding: 20rpx 24rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #ffffff;
  line-height: 36rpx;
  text-align: center;
}

/* 底部固定区域 */
.bottom-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 30rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 合计区域 */
.total-section {
  display: flex;
  align-items: center;
}

.total-label {
  font-size: 28rpx;
  color: #333333;
  margin-right: 8rpx;
}

.total-amount {
  font-size: 32rpx;
  color: #00c853;
  font-weight: 500;
}

/* 立即缴费按钮 */
.payment-btn {
  width: 200rpx;
  height: 72rpx;
  background-color: #00c853;
  color: #ffffff;
  border: none;
  border-radius: 36rpx;
  font-size: 28rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
}

.payment-btn.disabled {
  background-color: #cccccc;
  color: #999999;
}

.payment-btn:active:not(.disabled) {
  background-color: #00b248;
  transform: scale(0.98);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  line-height: 40rpx;
}

/* 响应式适配 */
@media (max-width: 480px) {
  .payment-list {
    padding: 20rpx 20rpx 0;
  }

  .payment-item {
    margin-bottom: 20rpx;
    padding: 24rpx;
  }

  .payment-title {
    font-size: 30rpx;
    margin-bottom: 24rpx;
  }

  .detail-label,
  .detail-value {
    font-size: 26rpx;
  }

  .detail-value.amount {
    font-size: 28rpx;
  }

  .notice-bar {
    margin: 0 20rpx 20rpx;
  }

  .payment-btn {
    width: 180rpx;
    height: 68rpx;
    font-size: 26rpx;
  }
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  margin-top: 20rpx;
}
</style>
