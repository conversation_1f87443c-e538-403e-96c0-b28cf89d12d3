
/**
 * @description: 动态注入脚本
 * @param {string} url
 * @param {function} callback
 * @param {string} scriptId 
 */
export function createScript(url, callback, scriptId = "jswxSdk") {
  if (document.querySelector(`#${scriptId}`)) return false;
  const jsapi = document.createElement("script");
  jsapi.charset = "utf-8";
  jsapi.src = url;
  jsapi.id = scriptId;
  document.head.appendChild(jsapi);
  const t = setTimeout(() => {
    clearTimeout(t);
    callback && callback();
  }, 100);
}


export function getURLParameters(url) {
  const params = {};
  const queryString = url.split('?')[1]; // 获取查询字符串部分
  if (queryString) {
    const pairs = queryString.split('&');
    for (let i = 0; i < pairs.length; i++) {
      const pair = pairs[i].split('=');
      if (pair.length === 2) {
        params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);
      }
    }
  }
  return params;
}

/**
 * @description: 字符串替换
 * @param {string} str 原字符
 * @param {number} index 下标
 * @param {string} replacement 替换字符
 * @return {string} 结果
 */
export function replaceAt(str, index, replacement) {
  return str.substr(0, index) + replacement + str.substr(index + replacement.length);
}

/**
 * @description: 去除url中的双斜杠
 * @param {string} url
 * @return {string} 
 */
export function removeUrlDouble(url) {
  try {
    const { origin, pathname } = new URL(url)
    const newPathname = pathname.replaceAll("//", "/")
    return `${origin}${newPathname}`
  } catch (error) {
    const arr = url?.split('//')
    if (arr.length > 2) {
      const idx = url.lastIndexOf("//")
      return replaceAt(url, idx, ' ').replace(/\s+/g, "")
    }
    return url
  }
}

// content 为后端返回的富文本内容 
export function formatImg(content) {
  if (!content) return '';
  const regex = /<img[^>]*>/g;
  content = content.replace(regex, function (match) {
    return match.replace('>', ' style="max-width:100%;height:auto;">');
  });
  return content;
}


export function maskPhoneNumber(phoneNumber) {
  if (phoneNumber.length !== 11) {
    return phoneNumber;
  }
  return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

export function clearBr(key) {
  key = key.replace(/<\/?.+?>/g, '');
  key = key.replace(/[\r\n]/g, '');
  return key;
}



export function getDate() {
  const date = new Date()
  const years = []
  const year = date.getFullYear() 
  const months = []
  const month = date.getMonth() + 1
  const days = []
  const day = date.getDate()
  for (let i = 1970; i <= date.getFullYear() -1 ; i++) {
    years.push(i)
  }
  for (let i = 1; i <= 12; i++) {
    months.push(i)
  }
  for (let i = 1; i <= 31; i++) {
    days.push(i)
  }
  return {
    years,
    year,
    months,
    month,
    days,
    day
  }
}

export function debounce(func, wait) {
  let timeout;
  return function() {
    const context = this;
    const args = arguments;
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      func.apply(context, args);
    }, wait);
  };
}

export function throttle(func, delay) {
  let lastTime = 0;
  
  return function(...args) {
    const now = Date.now();
    if (now - lastTime >= delay) {
      func.apply(this, args);
      lastTime = now;
    }
  };
}

export function countTotalCharacters(text, excludeSpaces = true) {
  if (excludeSpaces) {
    // 移除所有空格后计算长度
    return text.replace(/\s/g, '').length;
  }
  return text.length;
}