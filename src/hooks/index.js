import { ref, computed, nextTick } from "vue"

export function useShowLoading() {
    const loading = ref(false)
    return {
        loading: loading,
        startLoading(title = "Loading...") {
            const config = {}
            if (typeof title == 'string') {
                config['title'] = title
            }
            if (Object.prototype.toString.call(title) === '[object Object]') {
                config = {
                    ...title
                }
            }
            uni.showLoading(config)
            loading.value = true

        },
        stopLoading() {
            uni.hideLoading();
            loading.value = false
        }
    }
}




