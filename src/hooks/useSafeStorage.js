import { computed } from "vue";

export function useSafeStorage(key, defaultValue) {
  return computed(() => {
    try {
      const data = uni.getStorageSync(key);
      // 自动处理 JSON 解析
      if (typeof data === "string") {
        try {
          return JSON.parse(data) || defaultValue;
        } catch {
          return data || defaultValue;
        }
      }
      return data || defaultValue;
    } catch (e) {
      return defaultValue;
    }
  });
}
