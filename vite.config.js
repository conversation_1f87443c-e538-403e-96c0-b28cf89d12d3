import { defineConfig,loadEnv } from "vite"
import uni from "@dcloudio/vite-plugin-uni"
import path from "path"
import autoImport from "unplugin-auto-import/vite"
import components from "unplugin-vue-components/vite"

import { createHtmlPlugin } from "vite-plugin-html"

// https://vitejs.dev/config/

const versionTime = new Date().getTime()

function updateVersionPlugin(version = +new Date()) {
  return {
      name: "update-version-plugin",
      closeBundle() {
          if (process.env.UNI_PLATFORM == "h5") {
              console.log("H5平台生产version.json")
              const fs = require("fs")
              // eslint-disable-next-line quotes
              fs.writeFile("dist/build/h5/version.json", '{"version":' + version + "}\n", (err) => {
                  if (err) {
                      return console.log(err)
                  }
              })
          }
      }
  }
}

function isSubstrExist(str, substr) {
  var regex = new RegExp(substr)
  return regex.test(str)
}

const replaceFilePlugin = (replacements) => {
  return {
      name: "replace-file-plugin",
      enforce: "pre",
      // 在其他钩子中可以访问到配置
      transform(code, id) {
          if (["node_modules", "uni_modules"].find((i) => isSubstrExist(id, i))) return code
          if ([".vue", ".css", ".scss", "js"].find((i) => id.endsWith(i))) {
              Object.entries(replacements).forEach(([find, replaceWith]) => {
                  code = code.replace(new RegExp(find, "g"), replaceWith)
              })
              return code
          }
          return code
      }
  }
}

export default defineConfig(({ mode })=>{
  const env = loadEnv(mode, process.cwd())
  return  {
    resolve: {
        extensions: [".js", ".json", ".vue", ".scss"],
        alias: {
            "@": path.resolve(__dirname, "src")
        }
    },
    define:{
        __APP_VERSION__: JSON.stringify('v1.0.0'),
    },
    plugins: [
        uni(),
        autoImport({
            imports: ["vue", "uni-app"],
            dts: false,
            eslintrc: { enabled: false },
            vueTemplate: true,
            dirs: ["src/store", "src/utils", "src/hooks"]
        }),
        components({
            dirs: ["src/components"],
            extensions: ["vue"],
            deep: false
        }),
        createHtmlPlugin({
            minify: true,
            inject: {
                data: {
                    version: versionTime
                }
            }
        }),

        // replaceFilePlugin({
        //     "@nginx": env.VITE_BASE_STATIC
        // }),
    ],
    build: {
      rollupOptions: {
          plugins: [updateVersionPlugin(versionTime)]
      }
    }
  }
})
